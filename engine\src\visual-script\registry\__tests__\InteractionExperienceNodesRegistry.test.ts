/**
 * 交互体验系统节点注册表测试
 */

import { InteractionExperienceNodesRegistry } from '../InteractionExperienceNodesRegistry';
import { NodeRegistry } from '../NodeRegistry';

// Mock NodeRegistry
jest.mock('../NodeRegistry', () => ({
  NodeRegistry: {
    registerNode: jest.fn(),
    getRegisteredNodes: jest.fn(() => new Map()),
    isNodeRegistered: jest.fn(() => false)
  }
}));

describe('InteractionExperienceNodesRegistry', () => {
  let registry: InteractionExperienceNodesRegistry;
  let mockRegisterNode: jest.Mock;

  beforeEach(() => {
    registry = new InteractionExperienceNodesRegistry();
    mockRegisterNode = NodeRegistry.registerNode as jest.Mock;
    mockRegisterNode.mockClear();
  });

  describe('节点注册', () => {
    test('应该成功注册所有交互体验系统节点', () => {
      registry.registerAllNodes();

      expect(registry.isRegistered()).toBe(true);
      expect(registry.getRegisteredNodeCount()).toBe(58);
      expect(mockRegisterNode).toHaveBeenCalledTimes(58);
    });

    test('应该防止重复注册', () => {
      registry.registerAllNodes();
      registry.registerAllNodes(); // 第二次调用

      expect(mockRegisterNode).toHaveBeenCalledTimes(58); // 仍然是58次，没有重复
    });

    test('应该正确注册VR/AR节点', () => {
      registry.registerAllNodes();

      // 验证VR/AR核心节点注册
      expect(mockRegisterNode).toHaveBeenCalledWith(
        'VRControllerNode',
        expect.any(Function),
        expect.objectContaining({
          category: 'VR/AR',
          color: '#9C27B0',
          icon: 'vr-headset'
        })
      );

      // 验证VR/AR输入节点注册
      expect(mockRegisterNode).toHaveBeenCalledWith(
        'VRControllerInputNode',
        expect.any(Function),
        expect.objectContaining({
          category: 'VR/AR输入',
          color: '#673AB7',
          icon: 'input'
        })
      );
    });

    test('应该正确注册动作捕捉节点', () => {
      registry.registerAllNodes();

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'CameraInputNode',
        expect.any(Function),
        expect.objectContaining({
          category: '动作捕捉',
          color: '#FF5722',
          icon: 'motion-capture'
        })
      );

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'SkeletonTrackingNode',
        expect.any(Function),
        expect.objectContaining({
          category: '动作捕捉'
        })
      );
    });

    test('应该正确注册游戏逻辑节点', () => {
      registry.registerAllNodes();

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'GameStateNode',
        expect.any(Function),
        expect.objectContaining({
          category: '游戏逻辑',
          color: '#4CAF50',
          icon: 'gamepad'
        })
      );

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'PlayerControllerNode',
        expect.any(Function),
        expect.objectContaining({
          category: '游戏逻辑'
        })
      );
    });

    test('应该正确注册社交功能节点', () => {
      registry.registerAllNodes();

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'FriendSystemNode',
        expect.any(Function),
        expect.objectContaining({
          category: '社交功能',
          color: '#2196F3',
          icon: 'people'
        })
      );

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'ChatSystemNode',
        expect.any(Function),
        expect.objectContaining({
          category: '社交功能'
        })
      );
    });

    test('应该正确注册支付系统节点', () => {
      registry.registerAllNodes();

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'PaymentGatewayNode',
        expect.any(Function),
        expect.objectContaining({
          category: '支付系统',
          color: '#FF9800',
          icon: 'payment'
        })
      );

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'SubscriptionManagementNode',
        expect.any(Function),
        expect.objectContaining({
          category: '支付系统'
        })
      );
    });

    test('应该正确注册高级输入节点', () => {
      registry.registerAllNodes();

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'MultiTouchNode',
        expect.any(Function),
        expect.objectContaining({
          category: '高级输入',
          color: '#607D8B',
          icon: 'touch-app'
        })
      );

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'GestureRecognitionNode',
        expect.any(Function),
        expect.objectContaining({
          category: '高级输入'
        })
      );
    });

    test('应该正确注册传感器输入节点', () => {
      registry.registerAllNodes();

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'AccelerometerNode',
        expect.any(Function),
        expect.objectContaining({
          category: '传感器输入',
          color: '#795548',
          icon: 'sensors'
        })
      );

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'GyroscopeNode',
        expect.any(Function),
        expect.objectContaining({
          category: '传感器输入'
        })
      );
    });

    test('应该正确注册语音输入节点', () => {
      registry.registerAllNodes();

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'VoiceInputAdvancedNode',
        expect.any(Function),
        expect.objectContaining({
          category: '语音输入',
          color: '#E91E63',
          icon: 'mic'
        })
      );

      expect(mockRegisterNode).toHaveBeenCalledWith(
        'VoiceCommandAdvancedNode',
        expect.any(Function),
        expect.objectContaining({
          category: '语音输入'
        })
      );
    });
  });

  describe('统计信息', () => {
    test('应该返回正确的节点分类统计', () => {
      const stats = registry.getNodeCategoryStats();

      expect(stats).toEqual({
        'VR/AR': 10,
        'VR/AR输入': 8,
        '动作捕捉': 8,
        '游戏逻辑': 8,
        '社交功能': 6,
        '支付系统': 6,
        '高级输入': 4,
        '传感器输入': 6,
        '语音输入': 2
      });

      // 验证总数
      const totalNodes = Object.values(stats).reduce((sum, count) => sum + count, 0);
      expect(totalNodes).toBe(58);
    });

    test('应该返回所有已注册的节点类型', () => {
      registry.registerAllNodes();
      const nodeTypes = registry.getAllRegisteredNodeTypes();

      expect(nodeTypes).toHaveLength(58);
      expect(nodeTypes).toContain('VRControllerNode');
      expect(nodeTypes).toContain('GameStateNode');
      expect(nodeTypes).toContain('PaymentGatewayNode');
      expect(nodeTypes).toContain('MultiTouchNode');
      expect(nodeTypes).toContain('AccelerometerNode');
    });

    test('注册前应该返回未注册状态', () => {
      expect(registry.isRegistered()).toBe(false);
      expect(registry.getRegisteredNodeCount()).toBe(0);
    });

    test('注册后应该返回正确状态', () => {
      registry.registerAllNodes();

      expect(registry.isRegistered()).toBe(true);
      expect(registry.getRegisteredNodeCount()).toBe(58);
    });
  });

  describe('错误处理', () => {
    test('应该处理注册过程中的错误', () => {
      mockRegisterNode.mockImplementation(() => {
        throw new Error('注册失败');
      });

      expect(() => {
        registry.registerAllNodes();
      }).toThrow('注册失败');

      expect(registry.isRegistered()).toBe(false);
    });
  });

  describe('节点验证', () => {
    test('所有节点应该有正确的分类', () => {
      registry.registerAllNodes();

      const calls = mockRegisterNode.mock.calls;
      
      calls.forEach(([nodeName, nodeClass, config]) => {
        expect(config.category).toBeDefined();
        expect(config.description).toBeDefined();
        expect(config.color).toBeDefined();
        expect(config.icon).toBeDefined();
      });
    });

    test('节点名称应该唯一', () => {
      registry.registerAllNodes();

      const calls = mockRegisterNode.mock.calls;
      const nodeNames = calls.map(call => call[0]);
      const uniqueNames = new Set(nodeNames);

      expect(uniqueNames.size).toBe(nodeNames.length);
    });
  });
});
