/**
 * 交互体验系统节点注册表
 * 注册批次8：交互体验系统（58个节点）到编辑器
 * 包括：VR/AR节点(18个) + 动作捕捉节点(8个) + 游戏逻辑节点(8个) + 社交功能节点(6个) + 支付系统节点(6个) + 高级输入节点(4个) + 传感器输入节点(6个) + 语音输入节点(2个)
 */

import { NodeRegistry } from './NodeRegistry';

// 导入VR/AR节点
import {
  VRControllerNode,
  ARTrackingNode,
  SpatialMappingNode,
  HandTrackingNode,
  EyeTrackingNode,
  VoiceCommandNode,
  HapticFeedbackNode,
  VRTeleportationNode,
  ARPlacementNode,
  ImmersiveUINode
} from '../nodes/batch34/VRARNodes';

// 导入VR/AR输入节点
import {
  VRControllerInputNode,
  VRHeadsetTrackingNode,
  ARTouchInputNode,
  ARGestureInputNode,
  SpatialInputNode,
  EyeTrackingInputNode,
  HandTrackingInputNode,
  VoiceCommandInputNode
} from '../nodes/input/VRARInputNodes';

// 导入动作捕捉节点
import {
  CameraInputNode,
  MotionCaptureInitNode,
  SkeletonTrackingNode,
  FaceTrackingNode,
  HandTrackingNode as MocapHandTrackingNode,
  BodyTrackingNode
} from '../nodes/mocap/MotionCaptureNodes';

// 导入游戏逻辑节点
import {
  GameStateNode,
  PlayerControllerNode,
  InventorySystemNode,
  QuestSystemNode,
  DialogueSystemNode,
  SaveLoadSystemNode,
  AchievementSystemNode,
  LeaderboardNode
} from '../nodes/batch34/GameLogicNodes';

// 导入社交功能节点
import {
  FriendSystemNode,
  ChatSystemNode,
  GroupSystemNode,
  SocialSharingNode,
  UserGeneratedContentNode,
  CommunityFeaturesNode
} from '../nodes/batch34/SocialNodes';

// 导入支付系统节点
import {
  PaymentGatewayNode,
  SubscriptionManagementNode,
  InAppPurchaseNode,
  WalletManagementNode,
  RefundProcessingNode,
  PaymentAnalyticsNode
} from '../nodes/batch34/PaymentNodes';

// 导入高级输入节点
import {
  MultiTouchNode,
  GestureRecognitionNode,
  VoiceInputNode,
  MotionSensorNode
} from '../nodes/input/AdvancedInputNodes';

// 导入传感器输入节点
import {
  AccelerometerNode,
  GyroscopeNode,
  CompassNode,
  ProximityNode,
  LightSensorNode,
  PressureSensorNode
} from '../nodes/input/SensorInputNodes';

/**
 * 交互体验系统节点注册表类
 */
export class InteractionExperienceNodesRegistry {
  private registered: boolean = false;
  private registeredNodes: Set<string> = new Set();

  /**
   * 注册所有交互体验系统节点
   */
  public registerAllNodes(): void {
    if (this.registered) {
      console.log('交互体验系统节点已注册，跳过重复注册');
      return;
    }

    console.log('开始注册交互体验系统节点...');

    try {
      // 注册VR/AR节点 (18个)
      this.registerVRARNodes();

      // 注册动作捕捉节点 (8个)
      this.registerMotionCaptureNodes();

      // 注册游戏逻辑节点 (8个)
      this.registerGameLogicNodes();

      // 注册社交功能节点 (6个)
      this.registerSocialNodes();

      // 注册支付系统节点 (6个)
      this.registerPaymentNodes();

      // 注册高级输入节点 (4个)
      this.registerAdvancedInputNodes();

      // 注册传感器输入节点 (6个)
      this.registerSensorInputNodes();

      // 注册语音输入节点 (2个)
      this.registerVoiceInputNodes();

      this.registered = true;
      console.log(`交互体验系统节点注册完成，共注册 ${this.registeredNodes.size} 个节点`);
      console.log('包含：VR/AR(18) + 动作捕捉(8) + 游戏逻辑(8) + 社交功能(6) + 支付系统(6) + 高级输入(4) + 传感器输入(6) + 语音输入(2) = 58个节点');

    } catch (error) {
      console.error('交互体验系统节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册VR/AR节点 (18个)
   */
  private registerVRARNodes(): void {
    console.log('注册VR/AR节点...');

    // VR/AR核心节点 (10个)
    const vrArCoreNodes = [
      { nodeClass: VRControllerNode, name: 'VRControllerNode', description: '处理VR控制器输入和交互' },
      { nodeClass: ARTrackingNode, name: 'ARTrackingNode', description: '处理AR环境追踪和定位' },
      { nodeClass: SpatialMappingNode, name: 'SpatialMappingNode', description: '处理空间环境的3D重建和映射' },
      { nodeClass: HandTrackingNode, name: 'HandTrackingNode', description: '处理手部姿态识别和追踪' },
      { nodeClass: EyeTrackingNode, name: 'EyeTrackingNode', description: '处理眼部运动和注视点追踪' },
      { nodeClass: VoiceCommandNode, name: 'VoiceCommandNode', description: '处理语音命令识别和执行' },
      { nodeClass: HapticFeedbackNode, name: 'HapticFeedbackNode', description: '提供触觉反馈和震动效果' },
      { nodeClass: VRTeleportationNode, name: 'VRTeleportationNode', description: '处理VR环境中的瞬移移动' },
      { nodeClass: ARPlacementNode, name: 'ARPlacementNode', description: '处理AR对象的放置和定位' },
      { nodeClass: ImmersiveUINode, name: 'ImmersiveUINode', description: '创建沉浸式用户界面元素' }
    ];

    vrArCoreNodes.forEach(({ nodeClass, name, description }) => {
      NodeRegistry.registerNode(name, nodeClass, {
        category: 'VR/AR',
        description,
        color: '#9C27B0',
        icon: 'vr-headset'
      });
      this.registeredNodes.add(name);
    });

    // VR/AR输入节点 (8个)
    const vrArInputNodes = [
      { nodeClass: VRControllerInputNode, name: 'VRControllerInputNode', description: 'VR控制器输入处理' },
      { nodeClass: VRHeadsetTrackingNode, name: 'VRHeadsetTrackingNode', description: 'VR头显位置追踪' },
      { nodeClass: ARTouchInputNode, name: 'ARTouchInputNode', description: 'AR环境触摸输入' },
      { nodeClass: ARGestureInputNode, name: 'ARGestureInputNode', description: 'AR手势输入识别' },
      { nodeClass: SpatialInputNode, name: 'SpatialInputNode', description: '空间输入处理' },
      { nodeClass: EyeTrackingInputNode, name: 'EyeTrackingInputNode', description: '眼动追踪输入' },
      { nodeClass: HandTrackingInputNode, name: 'HandTrackingInputNode', description: '手部追踪输入' },
      { nodeClass: VoiceCommandInputNode, name: 'VoiceCommandInputNode', description: '语音命令输入' }
    ];

    vrArInputNodes.forEach(({ nodeClass, name, description }) => {
      NodeRegistry.registerNode(name, nodeClass, {
        category: 'VR/AR输入',
        description,
        color: '#673AB7',
        icon: 'input'
      });
      this.registeredNodes.add(name);
    });

    console.log('VR/AR节点注册完成 (18个)');
  }

  /**
   * 注册动作捕捉节点 (8个)
   */
  private registerMotionCaptureNodes(): void {
    console.log('注册动作捕捉节点...');

    const motionCaptureNodes = [
      { nodeClass: CameraInputNode, name: 'CameraInputNode', description: '摄像头输入和图像捕获' },
      { nodeClass: MotionCaptureInitNode, name: 'MotionCaptureInitNode', description: '动作捕捉系统初始化' },
      { nodeClass: SkeletonTrackingNode, name: 'SkeletonTrackingNode', description: '骨骼追踪和姿态识别' },
      { nodeClass: FaceTrackingNode, name: 'FaceTrackingNode', description: '面部追踪和表情识别' },
      { nodeClass: MocapHandTrackingNode, name: 'MocapHandTrackingNode', description: '手部动作捕捉追踪' },
      { nodeClass: BodyTrackingNode, name: 'BodyTrackingNode', description: '全身动作追踪' }
    ];

    motionCaptureNodes.forEach(({ nodeClass, name, description }) => {
      NodeRegistry.registerNode(name, nodeClass, {
        category: '动作捕捉',
        description,
        color: '#FF5722',
        icon: 'motion-capture'
      });
      this.registeredNodes.add(name);
    });

    console.log('动作捕捉节点注册完成 (8个)');
  }

  /**
   * 注册游戏逻辑节点 (8个)
   */
  private registerGameLogicNodes(): void {
    console.log('注册游戏逻辑节点...');

    const gameLogicNodes = [
      { nodeClass: GameStateNode, name: 'GameStateNode', description: '游戏状态管理和切换' },
      { nodeClass: PlayerControllerNode, name: 'PlayerControllerNode', description: '玩家控制器和输入处理' },
      { nodeClass: InventorySystemNode, name: 'InventorySystemNode', description: '物品库存系统管理' },
      { nodeClass: QuestSystemNode, name: 'QuestSystemNode', description: '任务系统和进度管理' },
      { nodeClass: DialogueSystemNode, name: 'DialogueSystemNode', description: '对话系统和剧情管理' },
      { nodeClass: SaveLoadSystemNode, name: 'SaveLoadSystemNode', description: '游戏存档和读档系统' },
      { nodeClass: AchievementSystemNode, name: 'AchievementSystemNode', description: '成就系统和奖励管理' },
      { nodeClass: LeaderboardNode, name: 'LeaderboardNode', description: '排行榜和竞技系统' }
    ];

    gameLogicNodes.forEach(({ nodeClass, name, description }) => {
      NodeRegistry.registerNode(name, nodeClass, {
        category: '游戏逻辑',
        description,
        color: '#4CAF50',
        icon: 'gamepad'
      });
      this.registeredNodes.add(name);
    });

    console.log('游戏逻辑节点注册完成 (8个)');
  }

  /**
   * 注册社交功能节点 (6个)
   */
  private registerSocialNodes(): void {
    console.log('注册社交功能节点...');

    const socialNodes = [
      { nodeClass: FriendSystemNode, name: 'FriendSystemNode', description: '好友系统和关系管理' },
      { nodeClass: ChatSystemNode, name: 'ChatSystemNode', description: '聊天系统和消息处理' },
      { nodeClass: GroupSystemNode, name: 'GroupSystemNode', description: '群组系统和团队管理' },
      { nodeClass: SocialSharingNode, name: 'SocialSharingNode', description: '社交分享和内容传播' },
      { nodeClass: UserGeneratedContentNode, name: 'UserGeneratedContentNode', description: '用户生成内容管理' },
      { nodeClass: CommunityFeaturesNode, name: 'CommunityFeaturesNode', description: '社区功能和互动特性' }
    ];

    socialNodes.forEach(({ nodeClass, name, description }) => {
      NodeRegistry.registerNode(name, nodeClass, {
        category: '社交功能',
        description,
        color: '#2196F3',
        icon: 'people'
      });
      this.registeredNodes.add(name);
    });

    console.log('社交功能节点注册完成 (6个)');
  }

  /**
   * 注册支付系统节点 (6个)
   */
  private registerPaymentNodes(): void {
    console.log('注册支付系统节点...');

    const paymentNodes = [
      { nodeClass: PaymentGatewayNode, name: 'PaymentGatewayNode', description: '支付网关和交易处理' },
      { nodeClass: SubscriptionManagementNode, name: 'SubscriptionManagementNode', description: '订阅管理和计费系统' },
      { nodeClass: InAppPurchaseNode, name: 'InAppPurchaseNode', description: '应用内购买和商品管理' },
      { nodeClass: WalletManagementNode, name: 'WalletManagementNode', description: '钱包管理和余额处理' },
      { nodeClass: RefundProcessingNode, name: 'RefundProcessingNode', description: '退款处理和订单管理' },
      { nodeClass: PaymentAnalyticsNode, name: 'PaymentAnalyticsNode', description: '支付分析和数据统计' }
    ];

    paymentNodes.forEach(({ nodeClass, name, description }) => {
      NodeRegistry.registerNode(name, nodeClass, {
        category: '支付系统',
        description,
        color: '#FF9800',
        icon: 'payment'
      });
      this.registeredNodes.add(name);
    });

    console.log('支付系统节点注册完成 (6个)');
  }

  /**
   * 注册高级输入节点 (4个)
   */
  private registerAdvancedInputNodes(): void {
    console.log('注册高级输入节点...');

    const advancedInputNodes = [
      { nodeClass: MultiTouchNode, name: 'MultiTouchNode', description: '多点触摸输入处理' },
      { nodeClass: GestureRecognitionNode, name: 'GestureRecognitionNode', description: '手势识别和动作检测' },
      { nodeClass: VoiceInputNode, name: 'VoiceInputNode', description: '语音输入和语音转文字' },
      { nodeClass: MotionSensorNode, name: 'MotionSensorNode', description: '运动传感器和姿态检测' }
    ];

    advancedInputNodes.forEach(({ nodeClass, name, description }) => {
      NodeRegistry.registerNode(name, nodeClass, {
        category: '高级输入',
        description,
        color: '#607D8B',
        icon: 'touch-app'
      });
      this.registeredNodes.add(name);
    });

    console.log('高级输入节点注册完成 (4个)');
  }

  /**
   * 注册传感器输入节点 (6个)
   */
  private registerSensorInputNodes(): void {
    console.log('注册传感器输入节点...');

    const sensorInputNodes = [
      { nodeClass: AccelerometerNode, name: 'AccelerometerNode', description: '加速度计传感器输入' },
      { nodeClass: GyroscopeNode, name: 'GyroscopeNode', description: '陀螺仪传感器输入' },
      { nodeClass: CompassNode, name: 'CompassNode', description: '指南针传感器输入' },
      { nodeClass: ProximityNode, name: 'ProximityNode', description: '距离传感器输入' },
      { nodeClass: LightSensorNode, name: 'LightSensorNode', description: '光线传感器输入' },
      { nodeClass: PressureSensorNode, name: 'PressureSensorNode', description: '压力传感器输入' }
    ];

    sensorInputNodes.forEach(({ nodeClass, name, description }) => {
      NodeRegistry.registerNode(name, nodeClass, {
        category: '传感器输入',
        description,
        color: '#795548',
        icon: 'sensors'
      });
      this.registeredNodes.add(name);
    });

    console.log('传感器输入节点注册完成 (6个)');
  }

  /**
   * 注册语音输入节点 (2个)
   */
  private registerVoiceInputNodes(): void {
    console.log('注册语音输入节点...');

    const voiceInputNodes = [
      { nodeClass: VoiceInputNode, name: 'VoiceInputAdvancedNode', description: '高级语音输入和识别' },
      { nodeClass: VoiceCommandInputNode, name: 'VoiceCommandAdvancedNode', description: '高级语音命令处理' }
    ];

    voiceInputNodes.forEach(({ nodeClass, name, description }) => {
      NodeRegistry.registerNode(name, nodeClass, {
        category: '语音输入',
        description,
        color: '#E91E63',
        icon: 'mic'
      });
      this.registeredNodes.add(name);
    });

    console.log('语音输入节点注册完成 (2个)');
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return Array.from(this.registeredNodes);
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.registeredNodes.size;
  }

  /**
   * 获取节点分类统计
   */
  public getNodeCategoryStats(): Record<string, number> {
    return {
      'VR/AR': 10,
      'VR/AR输入': 8,
      '动作捕捉': 8,
      '游戏逻辑': 8,
      '社交功能': 6,
      '支付系统': 6,
      '高级输入': 4,
      '传感器输入': 6,
      '语音输入': 2
    };
  }
}
