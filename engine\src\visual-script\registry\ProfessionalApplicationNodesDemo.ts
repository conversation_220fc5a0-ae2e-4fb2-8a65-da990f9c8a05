/**
 * 专业应用领域节点演示程序
 * 展示如何使用专业应用领域的各类节点
 */

import { professionalApplicationNodesRegistry, PROFESSIONAL_APPLICATION_NODE_TYPES } from './ProfessionalApplicationNodesRegistry';
import { NodeRegistry } from './NodeRegistry';

/**
 * 专业应用领域节点演示类
 */
export class ProfessionalApplicationNodesDemo {
  constructor() {
    // NodeRegistry是静态类，不需要实例化
  }

  /**
   * 运行完整演示
   */
  public async runFullDemo(): Promise<void> {
    console.log('🚀 开始专业应用领域节点演示...\n');

    try {
      // 1. 注册所有节点
      await this.registerNodes();

      // 2. 演示空间信息功能
      await this.demonstrateSpatialFeatures();

      // 3. 演示区块链功能
      await this.demonstrateBlockchainFeatures();

      // 4. 演示学习记录功能
      await this.demonstrateLearningFeatures();

      // 5. 演示RAG应用功能
      await this.demonstrateRAGFeatures();

      // 6. 演示协作功能
      await this.demonstrateCollaborationFeatures();

      // 7. 演示第三方集成功能
      await this.demonstrateThirdPartyFeatures();

      // 8. 演示材质编辑功能
      await this.demonstrateMaterialFeatures();

      // 9. 演示粒子编辑功能
      await this.demonstrateParticleFeatures();

      // 10. 显示统计信息
      this.showStatistics();

      console.log('\n✅ 专业应用领域节点演示完成！');

    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
      throw error;
    }
  }

  /**
   * 注册所有节点
   */
  private async registerNodes(): Promise<void> {
    console.log('📋 注册专业应用领域节点...');
    
    const startTime = performance.now();
    await professionalApplicationNodesRegistry.registerAllNodes();
    const endTime = performance.now();

    console.log(`✅ 节点注册完成，耗时: ${(endTime - startTime).toFixed(2)}ms\n`);
  }

  /**
   * 演示空间信息功能
   */
  private async demonstrateSpatialFeatures(): Promise<void> {
    console.log('🗺️  演示空间信息功能...');

    try {
      // 创建地理坐标节点
      const coordNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.CREATE_GEOGRAPHIC_COORDINATE);
      if (coordNode) {
        const result = await coordNode.execute({
          latitude: 39.9042,
          longitude: 116.4074,
          altitude: 50
        });
        console.log('  📍 创建地理坐标:', result);
      }

      // 坐标转换节点
      const transformNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.COORDINATE_TRANSFORM);
      if (transformNode) {
        const result = await transformNode.execute({
          sourceCoordinate: { lat: 39.9042, lng: 116.4074 },
          sourceCRS: 'WGS84',
          targetCRS: 'WebMercator'
        });
        console.log('  🔄 坐标转换:', result);
      }

      // 距离计算节点
      const distanceNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.CALCULATE_DISTANCE);
      if (distanceNode) {
        const result = await distanceNode.execute({
          point1: { lat: 39.9042, lng: 116.4074 },
          point2: { lat: 31.2304, lng: 121.4737 }
        });
        console.log('  📏 距离计算:', result);
      }

    } catch (error) {
      console.error('  ❌ 空间信息功能演示失败:', error);
    }

    console.log('');
  }

  /**
   * 演示区块链功能
   */
  private async demonstrateBlockchainFeatures(): Promise<void> {
    console.log('⛓️  演示区块链功能...');

    try {
      // 钱包连接节点
      const walletNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.WALLET_CONNECT);
      if (walletNode) {
        const result = await walletNode.execute({
          connect: true,
          network: 'ethereum',
          provider: 'metamask'
        });
        console.log('  💳 钱包连接:', result);
      }

      // 智能合约节点
      const contractNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.SMART_CONTRACT);
      if (contractNode) {
        const result = await contractNode.execute({
          deploy: true,
          contractCode: 'pragma solidity ^0.8.0; contract Demo { }',
          network: 'ethereum'
        });
        console.log('  📜 智能合约:', result);
      }

    } catch (error) {
      console.error('  ❌ 区块链功能演示失败:', error);
    }

    console.log('');
  }

  /**
   * 演示学习记录功能
   */
  private async demonstrateLearningFeatures(): Promise<void> {
    console.log('📚 演示学习记录功能...');

    try {
      // 学习记录节点
      const recordNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.LEARNING_RECORD);
      if (recordNode) {
        const result = await recordNode.execute({
          create: true,
          userId: 'user123',
          itemId: 'course001',
          itemType: 'course',
          title: 'DL引擎基础教程'
        });
        console.log('  📝 学习记录:', result);
      }

      // 学习路径节点
      const pathNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.LEARNING_PATH);
      if (pathNode) {
        const result = await pathNode.execute({
          create: true,
          name: '3D开发学习路径',
          description: '从基础到高级的3D开发学习路径'
        });
        console.log('  🛤️  学习路径:', result);
      }

    } catch (error) {
      console.error('  ❌ 学习记录功能演示失败:', error);
    }

    console.log('');
  }

  /**
   * 演示RAG应用功能
   */
  private async demonstrateRAGFeatures(): Promise<void> {
    console.log('🔍 演示RAG应用功能...');

    try {
      // 知识库节点
      const kbNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.KNOWLEDGE_BASE);
      if (kbNode) {
        const result = await kbNode.execute({
          create: true,
          name: 'DL引擎知识库',
          description: 'DL引擎相关技术文档和教程'
        });
        console.log('  📖 知识库:', result);
      }

      // RAG查询节点
      const ragNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.RAG_QUERY);
      if (ragNode) {
        const result = await ragNode.execute({
          query: true,
          question: '如何创建一个3D场景？',
          knowledgeBaseId: 'kb001'
        });
        console.log('  🤖 RAG查询:', result);
      }

    } catch (error) {
      console.error('  ❌ RAG应用功能演示失败:', error);
    }

    console.log('');
  }

  /**
   * 演示协作功能
   */
  private async demonstrateCollaborationFeatures(): Promise<void> {
    console.log('👥 演示协作功能...');

    try {
      // 协作会话节点
      const sessionNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.COLLABORATION_SESSION);
      if (sessionNode) {
        const result = await sessionNode.execute({
          create: true,
          sessionName: '3D场景协作编辑',
          maxUsers: 10
        });
        console.log('  🤝 协作会话:', result);
      }

      // 实时同步节点
      const syncNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.REAL_TIME_SYNC);
      if (syncNode) {
        const result = await syncNode.execute({
          sync: true,
          sessionId: 'session001',
          data: { type: 'sceneUpdate', content: 'object moved' }
        });
        console.log('  🔄 实时同步:', result);
      }

    } catch (error) {
      console.error('  ❌ 协作功能演示失败:', error);
    }

    console.log('');
  }

  /**
   * 演示第三方集成功能
   */
  private async demonstrateThirdPartyFeatures(): Promise<void> {
    console.log('🔌 演示第三方集成功能...');

    try {
      // 支付系统节点
      const paymentNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.PAYMENT_SYSTEM);
      if (paymentNode) {
        const result = await paymentNode.execute({
          createPayment: true,
          amount: 99.99,
          currency: 'USD',
          description: 'DL引擎专业版'
        });
        console.log('  💰 支付系统:', result);
      }

      // 第三方API节点
      const apiNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.THIRD_PARTY_API);
      if (apiNode) {
        const result = await apiNode.execute({
          call: true,
          url: 'https://api.example.com/data',
          method: 'GET'
        });
        console.log('  🌐 第三方API:', result);
      }

    } catch (error) {
      console.error('  ❌ 第三方集成功能演示失败:', error);
    }

    console.log('');
  }

  /**
   * 演示材质编辑功能
   */
  private async demonstrateMaterialFeatures(): Promise<void> {
    console.log('🎨 演示材质编辑功能...');

    try {
      // 材质编辑器节点
      const editorNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.MATERIAL_EDITOR);
      if (editorNode) {
        const result = await editorNode.execute({
          create: true,
          materialType: 'PBR',
          name: '金属材质'
        });
        console.log('  ✏️  材质编辑器:', result);
      }

      // 材质预览节点
      const previewNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.MATERIAL_PREVIEW);
      if (previewNode) {
        const result = await previewNode.execute({
          preview: true,
          materialId: 'material001',
          geometry: 'sphere'
        });
        console.log('  👁️  材质预览:', result);
      }

    } catch (error) {
      console.error('  ❌ 材质编辑功能演示失败:', error);
    }

    console.log('');
  }

  /**
   * 演示粒子编辑功能
   */
  private async demonstrateParticleFeatures(): Promise<void> {
    console.log('✨ 演示粒子编辑功能...');

    try {
      // 粒子系统编辑器节点
      const systemNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.PARTICLE_SYSTEM_EDITOR);
      if (systemNode) {
        const result = await systemNode.execute({
          create: true,
          systemName: '火焰效果',
          preset: 'fire'
        });
        console.log('  🔥 粒子系统编辑器:', result);
      }

      // 粒子预览节点
      const previewNode = NodeRegistry.createNode(PROFESSIONAL_APPLICATION_NODE_TYPES.PARTICLE_PREVIEW);
      if (previewNode) {
        const result = await previewNode.execute({
          preview: true,
          systemId: 'particle001',
          play: true
        });
        console.log('  👁️  粒子预览:', result);
      }

    } catch (error) {
      console.error('  ❌ 粒子编辑功能演示失败:', error);
    }

    console.log('');
  }

  /**
   * 显示统计信息
   */
  private showStatistics(): void {
    console.log('📊 专业应用领域节点统计信息:');
    
    const stats = professionalApplicationNodesRegistry.getRegistrationStats();
    const categories = professionalApplicationNodesRegistry.getNodeCategories();

    console.log(`  总节点数: ${stats.totalNodes}`);
    console.log(`  注册时间: ${stats.registrationTime.toFixed(2)}ms`);
    console.log(`  错误数量: ${stats.errors.length}`);
    
    console.log('\n  分类统计:');
    Object.entries(categories).forEach(([category, count]) => {
      console.log(`    ${category}: ${count}个节点`);
    });

    if (stats.errors.length > 0) {
      console.log('\n  错误列表:');
      stats.errors.forEach((error, index) => {
        console.log(`    ${index + 1}. ${error}`);
      });
    }
  }
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  const demo = new ProfessionalApplicationNodesDemo();
  demo.runFullDemo().catch(console.error);
}
