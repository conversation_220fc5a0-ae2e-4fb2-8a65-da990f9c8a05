// 简单的注册表测试
const { InteractionExperienceNodesRegistry } = require('./dist/visual-script/registry/InteractionExperienceNodesRegistry');

console.log('=== 交互体验系统节点注册表测试 ===');

try {
  const registry = new InteractionExperienceNodesRegistry();
  
  console.log('开始注册节点...');
  registry.registerAllNodes();
  
  console.log('注册完成！');
  console.log('已注册节点数量:', registry.getRegisteredNodeCount());
  console.log('注册状态:', registry.isRegistered());
  console.log('节点分类统计:', registry.getNodeCategoryStats());
  
  const nodeTypes = registry.getAllRegisteredNodeTypes();
  console.log('前10个节点类型:', nodeTypes.slice(0, 10));
  console.log('总节点类型数量:', nodeTypes.length);
  
  console.log('=== 测试完成 ===');
} catch (error) {
  console.error('测试失败:', error.message);
}
