/**
 * 交互体验系统节点演示
 * 展示如何使用批次8的58个交互体验系统节点
 * 包括：VR/AR、动作捕捉、游戏逻辑、社交功能、支付系统、高级输入、传感器输入、语音输入
 */

import { InteractionExperienceNodesRegistry } from '../src/visual-script/registry/InteractionExperienceNodesRegistry';

/**
 * 交互体验系统演示类
 */
class InteractionExperienceDemo {
  private registry: InteractionExperienceNodesRegistry;

  constructor() {
    this.registry = new InteractionExperienceNodesRegistry();
  }

  /**
   * 运行完整演示
   */
  public async runDemo(): Promise<void> {
    console.log('=== 交互体验系统节点演示开始 ===');

    try {
      // 注册所有节点
      await this.registerNodes();

      console.log('=== 交互体验系统节点演示完成 ===');

    } catch (error) {
      console.error('演示过程中发生错误:', error);
    }
  }

  /**
   * 注册所有节点
   */
  private async registerNodes(): Promise<void> {
    console.log('\n--- 注册交互体验系统节点 ---');

    this.registry.registerAllNodes();

    console.log(`节点注册完成，共注册 ${this.registry.getRegisteredNodeCount()} 个节点`);
    console.log('节点分类统计:', this.registry.getNodeCategoryStats());
    console.log('已注册的节点类型:', this.registry.getAllRegisteredNodeTypes().slice(0, 10), '...(共58个)');
  }





  /**
   * 演示游戏逻辑节点功能
   */
  private async demonstrateGameLogicNodes(): Promise<void> {
    console.log('\n--- 演示游戏逻辑节点功能 ---');

    // 游戏状态节点演示
    const gameState = new GameStateNode();
    const gameStateResult = gameState.execute({
      action: 'setState',
      state: 'playing',
      data: { level: 1, score: 0 }
    });
    console.log('游戏状态节点结果:', gameStateResult);

    // 玩家控制器节点演示
    const playerController = new PlayerControllerNode();
    const playerResult = playerController.execute({
      action: 'move',
      direction: { x: 1, y: 0, z: 0 },
      speed: 5.0,
      jump: false
    });
    console.log('玩家控制器节点结果:', playerResult);

    // 物品库存系统节点演示
    const inventory = new InventorySystemNode();
    const inventoryResult = inventory.execute({
      action: 'addItem',
      itemId: 'sword_001',
      quantity: 1,
      playerId: 'player_123'
    });
    console.log('物品库存系统节点结果:', inventoryResult);

    console.log('游戏逻辑节点演示完成');
  }

  /**
   * 演示社交功能节点
   */
  private async demonstrateSocialNodes(): Promise<void> {
    console.log('\n--- 演示社交功能节点 ---');

    // 好友系统节点演示
    const friendSystem = new FriendSystemNode();
    const friendResult = friendSystem.execute({
      action: 'addFriend',
      userId: 'user_123',
      friendId: 'user_456',
      message: '你好，我们做朋友吧！'
    });
    console.log('好友系统节点结果:', friendResult);

    // 聊天系统节点演示
    const chatSystem = new ChatSystemNode();
    const chatResult = chatSystem.execute({
      action: 'sendMessage',
      senderId: 'user_123',
      receiverId: 'user_456',
      message: '你好！',
      messageType: 'text'
    });
    console.log('聊天系统节点结果:', chatResult);

    // 群组系统节点演示
    const groupSystem = new GroupSystemNode();
    const groupResult = groupSystem.execute({
      action: 'createGroup',
      groupName: '游戏小队',
      creatorId: 'user_123',
      maxMembers: 10,
      isPrivate: false
    });
    console.log('群组系统节点结果:', groupResult);

    console.log('社交功能节点演示完成');
  }

  /**
   * 演示支付系统节点
   */
  private async demonstratePaymentNodes(): Promise<void> {
    console.log('\n--- 演示支付系统节点 ---');

    // 支付网关节点演示
    const paymentGateway = new PaymentGatewayNode();
    const paymentResult = paymentGateway.execute({
      amount: 99.99,
      currency: 'USD',
      paymentMethod: 'credit_card',
      merchantId: 'merchant_123',
      orderId: 'order_456',
      customerInfo: {
        name: '张三',
        email: '<EMAIL>'
      }
    });
    console.log('支付网关节点结果:', paymentResult);

    // 订阅管理节点演示
    const subscription = new SubscriptionManagementNode();
    const subscriptionResult = subscription.execute({
      action: 'create',
      customerId: 'customer_123',
      planId: 'premium_monthly',
      billingCycle: 'monthly',
      trialDays: 7
    });
    console.log('订阅管理节点结果:', subscriptionResult);

    // 钱包管理节点演示
    const wallet = new WalletManagementNode();
    const walletResult = wallet.execute({
      action: 'getBalance',
      userId: 'user_123',
      currency: 'USD'
    });
    console.log('钱包管理节点结果:', walletResult);

    console.log('支付系统节点演示完成');
  }

  /**
   * 演示高级输入节点
   */
  private async demonstrateAdvancedInputNodes(): Promise<void> {
    console.log('\n--- 演示高级输入节点 ---');

    // 多点触摸节点演示
    const multiTouch = new MultiTouchNode();
    const multiTouchResult = multiTouch.execute({
      enable: true,
      maxTouches: 5,
      minPressure: 0.1
    });
    console.log('多点触摸节点结果:', multiTouchResult);

    // 手势识别节点演示
    const gestureRecognition = new GestureRecognitionNode();
    const gestureResult = gestureRecognition.execute({
      enable: true,
      gestureTypes: ['tap', 'swipe', 'pinch', 'rotate'],
      minConfidence: 0.8
    });
    console.log('手势识别节点结果:', gestureResult);

    // 语音输入节点演示
    const voiceInput = new VoiceInputNode();
    const voiceResult = voiceInput.execute({
      enable: true,
      language: 'zh-CN',
      continuous: true,
      interimResults: true
    });
    console.log('语音输入节点结果:', voiceResult);

    // 运动传感器节点演示
    const motionSensor = new MotionSensorNode();
    const motionResult = motionSensor.execute({
      enable: true,
      sensitivity: 1.0,
      threshold: 0.5
    });
    console.log('运动传感器节点结果:', motionResult);

    console.log('高级输入节点演示完成');
  }

  /**
   * 演示传感器输入节点
   */
  private async demonstrateSensorInputNodes(): Promise<void> {
    console.log('\n--- 演示传感器输入节点 ---');

    // 加速度计节点演示
    const accelerometer = new AccelerometerNode();
    const accelResult = accelerometer.execute({
      enable: true,
      includeGravity: true,
      sensitivity: 1.0,
      threshold: 0.1
    });
    console.log('加速度计节点结果:', accelResult);

    // 陀螺仪节点演示
    const gyroscope = new GyroscopeNode();
    const gyroResult = gyroscope.execute({
      enable: true,
      sensitivity: 1.0,
      smoothing: 0.1
    });
    console.log('陀螺仪节点结果:', gyroResult);

    // 指南针节点演示
    const compass = new CompassNode();
    const compassResult = compass.execute({
      enable: true,
      magneticDeclination: 0,
      smoothing: 0.2
    });
    console.log('指南针节点结果:', compassResult);

    // 距离传感器节点演示
    const proximity = new ProximityNode();
    const proximityResult = proximity.execute({
      enable: true,
      threshold: 5.0
    });
    console.log('距离传感器节点结果:', proximityResult);

    // 光线传感器节点演示
    const lightSensor = new LightSensorNode();
    const lightResult = lightSensor.execute({
      enable: true,
      sensitivity: 1.0,
      darkThreshold: 10,
      brightThreshold: 1000
    });
    console.log('光线传感器节点结果:', lightResult);

    // 压力传感器节点演示
    const pressureSensor = new PressureSensorNode();
    const pressureResult = pressureSensor.execute({
      enable: true,
      sensitivity: 1.0,
      threshold: 0.5
    });
    console.log('压力传感器节点结果:', pressureResult);

    console.log('传感器输入节点演示完成');
  }

  /**
   * 演示语音输入节点
   */
  private async demonstrateVoiceInputNodes(): Promise<void> {
    console.log('\n--- 演示语音输入节点 ---');

    // 高级语音输入节点演示
    const advancedVoiceInput = new VoiceInputNode();
    const advancedVoiceResult = advancedVoiceInput.execute({
      enable: true,
      language: 'zh-CN',
      continuous: true,
      interimResults: true,
      maxAlternatives: 3
    });
    console.log('高级语音输入节点结果:', advancedVoiceResult);

    console.log('语音输入节点演示完成');
  }
}

/**
 * 运行演示
 */
async function runInteractionExperienceDemo(): Promise<void> {
  const demo = new InteractionExperienceDemo();
  await demo.runDemo();
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runInteractionExperienceDemo().catch(console.error);
}

export { InteractionExperienceDemo, runInteractionExperienceDemo };
