/**
 * 专业应用领域节点注册表
 * 注册批次9：专业应用领域（58个节点）
 * 包括空间信息、区块链、学习记录、RAG应用、协作功能、第三方集成、材质编辑、粒子编辑节点
 */

import { NodeRegistry } from './NodeRegistry';

// 导入空间信息节点 (19个)
import {
  CreateGeographicCoordinateNode,
  CoordinateTransformNode,
  CreateGeospatialComponentNode,
  AddGeospatialComponentNode,
  GetGeographicCoordinateNode,
  SetGeographicCoordinateNode,
  CalculateDistanceNode,
  BufferAnalysisNode,
  IntersectionAnalysisNode,
  PointInPolygonNode,
  CreateGeoJSONNode,
  CreateFromGeoJSONNode,
  SetMapViewNode,
  GetMapViewNode,
  SetMapProviderNode,
  SpatialQueryNode,
  GeofenceNode,
  RouteCalculationNode,
  ElevationQueryNode
} from '../nodes/spatial/SpatialNodes';

// 导入区块链节点 (3个)
import {
  WalletConnectNode,
  SmartContractNode,
  TransactionNode
} from '../nodes/blockchain/BlockchainNodes';

// 导入学习记录节点 (3个)
import {
  LearningRecordNode,
  LearningPathNode,
  KnowledgeGraphNode
} from '../nodes/learning/LearningRecordNodes';

// 导入RAG应用节点 (4个)
import {
  RAGQueryNode,
  KnowledgeBaseNode,
  DocumentIndexNode,
  SemanticSearchNode
} from '../nodes/rag/RAGApplicationNodes';

// 导入协作功能节点 (6个)
import {
  CollaborationSessionNode,
  UserPresenceNode,
  RealTimeSyncNode,
  ConflictResolutionNode,
  VersionControlNode,
  CommentSystemNode
} from '../nodes/collaboration/CollaborationNodes';

// 导入第三方集成节点 (5个)
import {
  PaymentSystemNode,
  ThirdPartyAPINode,
  WebhookNode,
  OAuthIntegrationNode,
  DataSyncNode
} from '../nodes/batch34/ThirdPartyIntegrationNodes';

// 导入材质编辑节点 (10个)
import {
  MaterialEditorNode,
  MaterialPreviewNode,
  MaterialLibraryNode,
  MaterialImportNode,
  MaterialExportNode,
  MaterialValidationNode,
  MaterialVersioningNode,
  MaterialSharingNode,
  MaterialAnalyticsNode
} from '../nodes/material/MaterialEditingNodes';

import {
  MaterialNodeEditorNode
} from '../nodes/material/MaterialEditingNodes2';

// 导入粒子编辑节点 (8个)
import {
  ParticleSystemEditorNode,
  ParticleEmitterEditorNode
} from '../nodes/particles/ParticleEditingNodes';

import {
  ParticlePreviewNode,
  ParticleLibraryNode,
  ParticleExportNode,
  ParticleImportNode,
  ParticleForceEditorNode,
  ParticleCollisionEditorNode
} from '../nodes/particles/ParticleEditingNodes2';

/**
 * 专业应用领域节点注册表类
 */
export class ProfessionalApplicationNodesRegistry {
  private nodeRegistry: NodeRegistry;
  private registered: boolean = false;
  private registrationStats = {
    spatialNodes: 0,
    blockchainNodes: 0,
    learningNodes: 0,
    ragNodes: 0,
    collaborationNodes: 0,
    thirdPartyNodes: 0,
    materialNodes: 0,
    particleNodes: 0,
    totalNodes: 0,
    registrationTime: 0,
    errors: [] as string[]
  };

  constructor(nodeRegistry: NodeRegistry) {
    this.nodeRegistry = nodeRegistry;
  }

  /**
   * 注册所有专业应用领域节点
   */
  public async registerAllNodes(): Promise<void> {
    if (this.registered) {
      console.warn('专业应用领域节点已经注册过了');
      return;
    }

    const startTime = performance.now();
    console.log('开始注册专业应用领域节点...');

    try {
      // 注册空间信息节点 (19个)
      await this.registerSpatialNodes();
      
      // 注册区块链节点 (3个)
      await this.registerBlockchainNodes();
      
      // 注册学习记录节点 (3个)
      await this.registerLearningNodes();
      
      // 注册RAG应用节点 (4个)
      await this.registerRAGNodes();
      
      // 注册协作功能节点 (6个)
      await this.registerCollaborationNodes();
      
      // 注册第三方集成节点 (5个)
      await this.registerThirdPartyNodes();
      
      // 注册材质编辑节点 (10个)
      await this.registerMaterialNodes();
      
      // 注册粒子编辑节点 (8个)
      await this.registerParticleNodes();

      this.registered = true;
      this.registrationStats.registrationTime = performance.now() - startTime;
      this.registrationStats.totalNodes = this.calculateTotalNodes();

      console.log('专业应用领域节点注册完成！', {
        totalNodes: this.registrationStats.totalNodes,
        registrationTime: `${this.registrationStats.registrationTime.toFixed(2)}ms`,
        breakdown: {
          spatial: this.registrationStats.spatialNodes,
          blockchain: this.registrationStats.blockchainNodes,
          learning: this.registrationStats.learningNodes,
          rag: this.registrationStats.ragNodes,
          collaboration: this.registrationStats.collaborationNodes,
          thirdParty: this.registrationStats.thirdPartyNodes,
          material: this.registrationStats.materialNodes,
          particle: this.registrationStats.particleNodes
        }
      });

    } catch (error) {
      console.error('专业应用领域节点注册失败:', error);
      this.registrationStats.errors.push(error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * 注册空间信息节点 (19个)
   */
  private async registerSpatialNodes(): Promise<void> {
    console.log('注册空间信息节点...');

    const spatialNodes = [
      { nodeClass: CreateGeographicCoordinateNode, type: 'CreateGeographicCoordinate', name: '创建地理坐标', desc: '创建地理坐标点' },
      { nodeClass: CoordinateTransformNode, type: 'CoordinateTransform', name: '坐标转换', desc: '地理坐标系转换' },
      { nodeClass: CreateGeospatialComponentNode, type: 'CreateGeospatialComponent', name: '创建地理空间组件', desc: '创建地理空间组件' },
      { nodeClass: AddGeospatialComponentNode, type: 'AddGeospatialComponent', name: '添加地理空间组件', desc: '添加地理空间组件到实体' },
      { nodeClass: GetGeographicCoordinateNode, type: 'GetGeographicCoordinate', name: '获取地理坐标', desc: '获取实体的地理坐标' },
      { nodeClass: SetGeographicCoordinateNode, type: 'SetGeographicCoordinate', name: '设置地理坐标', desc: '设置实体的地理坐标' },
      { nodeClass: CalculateDistanceNode, type: 'CalculateDistance', name: '计算距离', desc: '计算两点间的地理距离' },
      { nodeClass: BufferAnalysisNode, type: 'BufferAnalysis', name: '缓冲区分析', desc: '地理缓冲区分析' },
      { nodeClass: IntersectionAnalysisNode, type: 'IntersectionAnalysis', name: '相交分析', desc: '地理要素相交分析' },
      { nodeClass: PointInPolygonNode, type: 'PointInPolygon', name: '点在多边形内', desc: '判断点是否在多边形内' },
      { nodeClass: CreateGeoJSONNode, type: 'CreateGeoJSON', name: '创建GeoJSON', desc: '创建GeoJSON数据' },
      { nodeClass: CreateFromGeoJSONNode, type: 'CreateFromGeoJSON', name: '从GeoJSON创建', desc: '从GeoJSON数据创建地理要素' },
      { nodeClass: SetMapViewNode, type: 'SetMapView', name: '设置地图视图', desc: '设置地图视图参数' },
      { nodeClass: GetMapViewNode, type: 'GetMapView', name: '获取地图视图', desc: '获取当前地图视图' },
      { nodeClass: SetMapProviderNode, type: 'SetMapProvider', name: '设置地图提供商', desc: '设置地图瓦片提供商' }
    ];

    for (const { nodeClass, type, name, desc } of spatialNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '空间信息',
        desc,
        'map',
        '#4CAF50'
      );
    }

    this.registrationStats.spatialNodes = spatialNodes.length;
    console.log(`空间信息节点注册完成 - ${spatialNodes.length}个节点`);
  }

  /**
   * 注册区块链节点 (3个)
   */
  private async registerBlockchainNodes(): Promise<void> {
    console.log('注册区块链节点...');

    const blockchainNodes = [
      { nodeClass: WalletConnectNode, type: 'WalletConnect', name: '钱包连接', desc: '连接和管理区块链钱包' },
      { nodeClass: SmartContractNode, type: 'SmartContract', name: '智能合约', desc: '部署和调用智能合约' },
      { nodeClass: TransactionNode, type: 'Transaction', name: '区块链交易', desc: '发送和监控区块链交易' }
    ];

    for (const { nodeClass, type, name, desc } of blockchainNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '区块链',
        desc,
        'account_balance_wallet',
        '#FF9800'
      );
    }

    this.registrationStats.blockchainNodes = blockchainNodes.length;
    console.log(`区块链节点注册完成 - ${blockchainNodes.length}个节点`);
  }

  /**
   * 注册学习记录节点 (3个)
   */
  private async registerLearningNodes(): Promise<void> {
    console.log('注册学习记录节点...');

    const learningNodes = [
      { nodeClass: LearningRecordNode, type: 'LearningRecord', name: '学习记录', desc: '管理学习记录和进度' },
      { nodeClass: LearningPathNode, type: 'LearningPath', name: '学习路径', desc: '创建和管理学习路径' },
      { nodeClass: KnowledgeGraphNode, type: 'KnowledgeGraph', name: '知识图谱', desc: '构建和查询知识图谱' }
    ];

    for (const { nodeClass, type, name, desc } of learningNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '学习记录',
        desc,
        'school',
        '#9C27B0'
      );
    }

    this.registrationStats.learningNodes = learningNodes.length;
    console.log(`学习记录节点注册完成 - ${learningNodes.length}个节点`);
  }

  /**
   * 注册RAG应用节点 (4个)
   */
  private async registerRAGNodes(): Promise<void> {
    console.log('注册RAG应用节点...');

    const ragNodes = [
      { nodeClass: RAGQueryNode, type: 'RAGQuery', name: 'RAG查询', desc: '执行检索增强生成查询' },
      { nodeClass: KnowledgeBaseNode, type: 'KnowledgeBase', name: '知识库', desc: '管理RAG知识库' },
      { nodeClass: DocumentIndexNode, type: 'DocumentIndex', name: '文档索引', desc: '创建和管理文档索引' },
      { nodeClass: SemanticSearchNode, type: 'SemanticSearch', name: '语义搜索', desc: '执行语义搜索查询' }
    ];

    for (const { nodeClass, type, name, desc } of ragNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        'RAG应用',
        desc,
        'search',
        '#607D8B'
      );
    }

    this.registrationStats.ragNodes = ragNodes.length;
    console.log(`RAG应用节点注册完成 - ${ragNodes.length}个节点`);
  }

  /**
   * 注册协作功能节点 (6个)
   */
  private async registerCollaborationNodes(): Promise<void> {
    console.log('注册协作功能节点...');

    const collaborationNodes = [
      { nodeClass: CollaborationSessionNode, type: 'CollaborationSession', name: '协作会话', desc: '创建和管理协作会话' },
      { nodeClass: UserPresenceNode, type: 'UserPresence', name: '用户状态', desc: '管理用户在线状态' },
      { nodeClass: RealTimeSyncNode, type: 'RealTimeSync', name: '实时同步', desc: '实时数据同步' },
      { nodeClass: ConflictResolutionNode, type: 'ConflictResolution', name: '冲突解决', desc: '处理协作冲突' },
      { nodeClass: VersionControlNode, type: 'VersionControl', name: '版本控制', desc: '管理版本控制' },
      { nodeClass: CommentSystemNode, type: 'CommentSystem', name: '评论系统', desc: '管理评论和反馈' }
    ];

    for (const { nodeClass, type, name, desc } of collaborationNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '协作功能',
        desc,
        'group',
        '#3F51B5'
      );
    }

    this.registrationStats.collaborationNodes = collaborationNodes.length;
    console.log(`协作功能节点注册完成 - ${collaborationNodes.length}个节点`);
  }

  /**
   * 注册第三方集成节点 (5个)
   */
  private async registerThirdPartyNodes(): Promise<void> {
    console.log('注册第三方集成节点...');

    const thirdPartyNodes = [
      { nodeClass: PaymentSystemNode, type: 'PaymentSystem', name: '支付系统', desc: '集成支付系统' },
      { nodeClass: ThirdPartyAPINode, type: 'ThirdPartyAPI', name: '第三方API', desc: '调用第三方API' },
      { nodeClass: WebhookNode, type: 'Webhook', name: 'Webhook', desc: '处理Webhook回调' },
      { nodeClass: OAuthIntegrationNode, type: 'OAuthIntegration', name: 'OAuth集成', desc: 'OAuth认证集成' },
      { nodeClass: DataSyncNode, type: 'DataSync', name: '数据同步', desc: '第三方数据同步' }
    ];

    for (const { nodeClass, type, name, desc } of thirdPartyNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '第三方集成',
        desc,
        'extension',
        '#795548'
      );
    }

    this.registrationStats.thirdPartyNodes = thirdPartyNodes.length;
    console.log(`第三方集成节点注册完成 - ${thirdPartyNodes.length}个节点`);
  }

  /**
   * 注册材质编辑节点 (10个)
   */
  private async registerMaterialNodes(): Promise<void> {
    console.log('注册材质编辑节点...');

    const materialNodes = [
      { nodeClass: MaterialEditorNode, type: 'MaterialEditor', name: '材质编辑器', desc: '创建和编辑材质' },
      { nodeClass: MaterialPreviewNode, type: 'MaterialPreview', name: '材质预览', desc: '预览材质效果' },
      { nodeClass: MaterialLibraryNode, type: 'MaterialLibrary', name: '材质库', desc: '管理材质库' },
      { nodeClass: MaterialImportNode, type: 'MaterialImport', name: '材质导入', desc: '导入外部材质' },
      { nodeClass: MaterialExportNode, type: 'MaterialExport', name: '材质导出', desc: '导出材质文件' },
      { nodeClass: MaterialValidationNode, type: 'MaterialValidation', name: '材质验证', desc: '验证材质有效性' },
      { nodeClass: MaterialVersioningNode, type: 'MaterialVersioning', name: '材质版本控制', desc: '管理材质版本' },
      { nodeClass: MaterialSharingNode, type: 'MaterialSharing', name: '材质共享', desc: '共享材质资源' },
      { nodeClass: MaterialAnalyticsNode, type: 'MaterialAnalytics', name: '材质分析', desc: '分析材质性能' },
      { nodeClass: MaterialNodeEditorNode, type: 'MaterialNodeEditor', name: '材质节点编辑器', desc: '可视化材质节点编辑' }
    ];

    for (const { nodeClass, type, name, desc } of materialNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '材质编辑',
        desc,
        'palette',
        '#E91E63'
      );
    }

    this.registrationStats.materialNodes = materialNodes.length;
    console.log(`材质编辑节点注册完成 - ${materialNodes.length}个节点`);
  }

  /**
   * 注册粒子编辑节点 (8个)
   */
  private async registerParticleNodes(): Promise<void> {
    console.log('注册粒子编辑节点...');

    const particleNodes = [
      { nodeClass: ParticleSystemEditorNode, type: 'ParticleSystemEditor', name: '粒子系统编辑器', desc: '创建和编辑粒子系统' },
      { nodeClass: ParticleEmitterEditorNode, type: 'ParticleEmitterEditor', name: '粒子发射器编辑器', desc: '编辑粒子发射器' },
      { nodeClass: ParticlePreviewNode, type: 'ParticlePreview', name: '粒子预览', desc: '预览粒子效果' },
      { nodeClass: ParticleLibraryNode, type: 'ParticleLibrary', name: '粒子库', desc: '管理粒子效果库' },
      { nodeClass: ParticleExportNode, type: 'ParticleExport', name: '粒子导出', desc: '导出粒子效果' },
      { nodeClass: ParticleImportNode, type: 'ParticleImport', name: '粒子导入', desc: '导入粒子效果' },
      { nodeClass: ParticleForceEditorNode, type: 'ParticleForceEditor', name: '粒子力场编辑器', desc: '编辑粒子力场' },
      { nodeClass: ParticleCollisionEditorNode, type: 'ParticleCollisionEditor', name: '粒子碰撞编辑器', desc: '编辑粒子碰撞' }
    ];

    for (const { nodeClass, type, name, desc } of particleNodes) {
      this.nodeRegistry.registerNode(
        type,
        nodeClass,
        '粒子编辑',
        desc,
        'grain',
        '#FF5722'
      );
    }

    this.registrationStats.particleNodes = particleNodes.length;
    console.log(`粒子编辑节点注册完成 - ${particleNodes.length}个节点`);
  }

  /**
   * 计算总节点数
   */
  private calculateTotalNodes(): number {
    return this.registrationStats.spatialNodes +
           this.registrationStats.blockchainNodes +
           this.registrationStats.learningNodes +
           this.registrationStats.ragNodes +
           this.registrationStats.collaborationNodes +
           this.registrationStats.thirdPartyNodes +
           this.registrationStats.materialNodes +
           this.registrationStats.particleNodes;
  }

  /**
   * 获取注册统计信息
   */
  public getRegistrationStats() {
    return {
      ...this.registrationStats,
      isRegistered: this.registered
    };
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 空间信息节点 (19个)
      'CreateGeographicCoordinate', 'CoordinateTransform', 'CreateGeospatialComponent',
      'AddGeospatialComponent', 'GetGeographicCoordinate', 'SetGeographicCoordinate',
      'CalculateDistance', 'BufferAnalysis', 'IntersectionAnalysis', 'PointInPolygon',
      'CreateGeoJSON', 'CreateFromGeoJSON', 'SetMapView', 'GetMapView', 'SetMapProvider',

      // 区块链节点 (3个)
      'WalletConnect', 'SmartContract', 'Transaction',

      // 学习记录节点 (3个)
      'LearningRecord', 'LearningPath', 'KnowledgeGraph',

      // RAG应用节点 (4个)
      'RAGQuery', 'KnowledgeBase', 'DocumentIndex', 'SemanticSearch',

      // 协作功能节点 (6个)
      'CollaborationSession', 'UserPresence', 'RealTimeSync',
      'ConflictResolution', 'VersionControl', 'CommentSystem',

      // 第三方集成节点 (5个)
      'PaymentSystem', 'ThirdPartyAPI', 'Webhook', 'OAuthIntegration', 'DataSync',

      // 材质编辑节点 (10个)
      'MaterialEditor', 'MaterialPreview', 'MaterialLibrary', 'MaterialImport',
      'MaterialExport', 'MaterialValidation', 'MaterialVersioning', 'MaterialSharing',
      'MaterialAnalytics', 'MaterialNodeEditor',

      // 粒子编辑节点 (8个)
      'ParticleSystemEditor', 'ParticleEmitterEditor', 'ParticlePreview', 'ParticleLibrary',
      'ParticleExport', 'ParticleImport', 'ParticleForceEditor', 'ParticleCollisionEditor'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取节点分类信息
   */
  public getNodeCategories(): Record<string, number> {
    return {
      '空间信息': this.registrationStats.spatialNodes,
      '区块链': this.registrationStats.blockchainNodes,
      '学习记录': this.registrationStats.learningNodes,
      'RAG应用': this.registrationStats.ragNodes,
      '协作功能': this.registrationStats.collaborationNodes,
      '第三方集成': this.registrationStats.thirdPartyNodes,
      '材质编辑': this.registrationStats.materialNodes,
      '粒子编辑': this.registrationStats.particleNodes
    };
  }

  /**
   * 重置注册状态（用于测试）
   */
  public reset(): void {
    this.registered = false;
    this.registrationStats = {
      spatialNodes: 0,
      blockchainNodes: 0,
      learningNodes: 0,
      ragNodes: 0,
      collaborationNodes: 0,
      thirdPartyNodes: 0,
      materialNodes: 0,
      particleNodes: 0,
      totalNodes: 0,
      registrationTime: 0,
      errors: []
    };
  }
}

// 创建全局实例
export const professionalApplicationNodesRegistry = new ProfessionalApplicationNodesRegistry(
  NodeRegistry.getInstance()
);

// 导出节点类型常量
export const PROFESSIONAL_APPLICATION_NODE_TYPES = {
  // 空间信息节点
  CREATE_GEOGRAPHIC_COORDINATE: 'CreateGeographicCoordinate',
  COORDINATE_TRANSFORM: 'CoordinateTransform',
  CREATE_GEOSPATIAL_COMPONENT: 'CreateGeospatialComponent',
  ADD_GEOSPATIAL_COMPONENT: 'AddGeospatialComponent',
  GET_GEOGRAPHIC_COORDINATE: 'GetGeographicCoordinate',
  SET_GEOGRAPHIC_COORDINATE: 'SetGeographicCoordinate',
  CALCULATE_DISTANCE: 'CalculateDistance',
  BUFFER_ANALYSIS: 'BufferAnalysis',
  INTERSECTION_ANALYSIS: 'IntersectionAnalysis',
  POINT_IN_POLYGON: 'PointInPolygon',
  CREATE_GEOJSON: 'CreateGeoJSON',
  CREATE_FROM_GEOJSON: 'CreateFromGeoJSON',
  SET_MAP_VIEW: 'SetMapView',
  GET_MAP_VIEW: 'GetMapView',
  SET_MAP_PROVIDER: 'SetMapProvider',

  // 区块链节点
  WALLET_CONNECT: 'WalletConnect',
  SMART_CONTRACT: 'SmartContract',
  TRANSACTION: 'Transaction',

  // 学习记录节点
  LEARNING_RECORD: 'LearningRecord',
  LEARNING_PATH: 'LearningPath',
  KNOWLEDGE_GRAPH: 'KnowledgeGraph',

  // RAG应用节点
  RAG_QUERY: 'RAGQuery',
  KNOWLEDGE_BASE: 'KnowledgeBase',
  DOCUMENT_INDEX: 'DocumentIndex',
  SEMANTIC_SEARCH: 'SemanticSearch',

  // 协作功能节点
  COLLABORATION_SESSION: 'CollaborationSession',
  USER_PRESENCE: 'UserPresence',
  REAL_TIME_SYNC: 'RealTimeSync',
  CONFLICT_RESOLUTION: 'ConflictResolution',
  VERSION_CONTROL: 'VersionControl',
  COMMENT_SYSTEM: 'CommentSystem',

  // 第三方集成节点
  PAYMENT_SYSTEM: 'PaymentSystem',
  THIRD_PARTY_API: 'ThirdPartyAPI',
  WEBHOOK: 'Webhook',
  OAUTH_INTEGRATION: 'OAuthIntegration',
  DATA_SYNC: 'DataSync',

  // 材质编辑节点
  MATERIAL_EDITOR: 'MaterialEditor',
  MATERIAL_PREVIEW: 'MaterialPreview',
  MATERIAL_LIBRARY: 'MaterialLibrary',
  MATERIAL_IMPORT: 'MaterialImport',
  MATERIAL_EXPORT: 'MaterialExport',
  MATERIAL_VALIDATION: 'MaterialValidation',
  MATERIAL_VERSIONING: 'MaterialVersioning',
  MATERIAL_SHARING: 'MaterialSharing',
  MATERIAL_ANALYTICS: 'MaterialAnalytics',
  MATERIAL_NODE_EDITOR: 'MaterialNodeEditor',

  // 粒子编辑节点
  PARTICLE_SYSTEM_EDITOR: 'ParticleSystemEditor',
  PARTICLE_EMITTER_EDITOR: 'ParticleEmitterEditor',
  PARTICLE_PREVIEW: 'ParticlePreview',
  PARTICLE_LIBRARY: 'ParticleLibrary',
  PARTICLE_EXPORT: 'ParticleExport',
  PARTICLE_IMPORT: 'ParticleImport',
  PARTICLE_FORCE_EDITOR: 'ParticleForceEditor',
  PARTICLE_COLLISION_EDITOR: 'ParticleCollisionEditor'
} as const;
